name: Release

on:
  push:
    branches: [ "release" ]

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
      - run: rustup toolchain install stable --profile minimal
      - uses: Swatinem/rust-cache@v2
      - run: rustup target add aarch64-linux-android armv7-linux-androideabi i686-linux-android x86_64-linux-android
      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: stable
          flutter-version: 3.27.4
      - name: Install cargo-binstall
        uses: cargo-bins/cargo-binstall@v1.14.1
      - run: cargo binstall rinf_cli --no-confirm
      - run: rinf gen
      - run: cargo binstall cargo-ndk --no-confirm
      - run: cargo ndk -t armeabi-v7a -t arm64-v8a -t x86 -t x86_64 build --release

      - run: mkdir -p android/app/src/main/jniLibs/armeabi-v7a
      - run: cp -r target/armv7-linux-androideabi/release/libhub.so android/app/src/main/jniLibs/armeabi-v7a/

      - run: mkdir -p android/app/src/main/jniLibs/arm64-v8a
      - run: cp -r target/aarch64-linux-android/release/libhub.so android/app/src/main/jniLibs/arm64-v8a/

      - run: mkdir -p android/app/src/main/jniLibs/x86
      - run: cp -r target/i686-linux-android/release/libhub.so android/app/src/main/jniLibs/x86/

      - run: mkdir -p android/app/src/main/jniLibs/x86_64
      - run: cp -r target/x86_64-linux-android/release/libhub.so android/app/src/main/jniLibs/x86_64/

      - name: Build Android
        run: flutter build apk --release
      - name: Release
        uses: ncipollo/release-action@v1
        with:
          artifacts: build/app/outputs/flutter-apk/app-release.apk
          allowUpdates: true
          tag: release-build